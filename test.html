<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام مطعم بازوكا</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #27ae60; background: #d5f4e6; }
        .error { border-color: #e74c3c; background: #fdf2f2; }
        .info { border-color: #3498db; background: #ebf3fd; }
        h1 { color: #2c3e50; text-align: center; }
        h2 { color: #34495e; }
        .btn {
            background: #ff6b35;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background: #e55a2b; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🍽️ نظام مطعم بازوكا - صفحة الاختبار</h1>
        
        <div class="test-item success">
            <h2>✅ تم إنشاء النظام بنجاح!</h2>
            <p>تم إنشاء نظام مطعم بازوكا بجميع المكونات المطلوبة:</p>
            <ul>
                <li>الصفحة الرئيسية مع قائمة الطعام</li>
                <li>نظام السلة والطلبات</li>
                <li>نظام حجز الطاولات</li>
                <li>لوحة الإدارة الشاملة</li>
                <li>تصميم متجاوب وجذاب</li>
            </ul>
        </div>

        <div class="test-item info">
            <h2>🔗 روابط النظام</h2>
            <p>يمكنك الوصول إلى أجزاء النظام المختلفة من خلال الروابط التالية:</p>
            <a href="index.html" class="btn">الصفحة الرئيسية</a>
            <a href="login.html" class="btn">صفحة تسجيل الدخول الموحدة</a>
            <a href="admin-login.html" class="btn">تسجيل دخول الإدارة</a>
            <a href="dashboard.html" class="btn">لوحة التحكم المتقدمة</a>
            <a href="admin.html" class="btn">لوحة الإدارة التقليدية</a>
        </div>

        <div class="test-item success">
            <h2>🔐 نظام المصادقة المتقدم</h2>
            <p>تم تطوير نظام مصادقة متقدم جداً يشمل:</p>
            <ul>
                <li><strong>تسجيل دخول آمن:</strong> مع التحقق من صحة البيانات</li>
                <li><strong>إنشاء حسابات جديدة:</strong> مع التحقق من قوة كلمة المرور</li>
                <li><strong>التحقق بخطوتين:</strong> رموز تحقق عبر الرسائل النصية</li>
                <li><strong>استعادة كلمة المرور:</strong> نظام آمن لاستعادة الحسابات</li>
                <li><strong>حماية من الهجمات:</strong> منع محاولات الدخول المتكررة</li>
                <li><strong>جلسات آمنة:</strong> انتهاء صلاحية تلقائي</li>
                <li><strong>مراقبة الأمان:</strong> تسجيل الأنشطة المشبوهة</li>
            </ul>

            <h3>حسابات تجريبية:</h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
                <h4>للعملاء:</h4>
                <p><strong>البريد:</strong> <EMAIL></p>
                <p><strong>كلمة المرور:</strong> User@123</p>

                <h4>للإدارة (يوجه للوحة التحكم المتقدمة):</h4>
                <p><strong>البريد:</strong> <EMAIL></p>
                <p><strong>كلمة المرور:</strong> Admin@123</p>
                <p><strong>رمز التحقق:</strong> 123456 (اختياري)</p>
                <p style="color: #28a745; font-weight: 600;">✅ عند تسجيل الدخول من الصفحة الرئيسية، سيتم توجيه المدير تلقائياً للوحة التحكم المتقدمة</p>
            </div>
        </div>

        <div class="test-item success">
            <h2>🚀 لوحة التحكم المتقدمة</h2>
            <p>تم تطوير لوحة تحكم احترافية ومتقدمة جداً تشمل:</p>
            <ul>
                <li><strong>واجهة حديثة ومتجاوبة:</strong> تصميم عصري مع تأثيرات بصرية متقدمة</li>
                <li><strong>إحصائيات مباشرة:</strong> بيانات فورية ومؤشرات أداء</li>
                <li><strong>رسوم بيانية تفاعلية:</strong> مخططات متقدمة باستخدام Chart.js</li>
                <li><strong>بحث شامل:</strong> بحث سريع في جميع أجزاء النظام</li>
                <li><strong>إشعارات ذكية:</strong> تنبيهات فورية للأحداث المهمة</li>
                <li><strong>إجراءات سريعة:</strong> وصول سريع للمهام الشائعة</li>
                <li><strong>تحديثات مباشرة:</strong> بيانات محدثة في الوقت الفعلي</li>
                <li><strong>تخصيص كامل:</strong> إمكانية تخصيص الواجهة</li>
            </ul>

            <h3>المميزات التقنية:</h3>
            <ul>
                <li>📊 <strong>Chart.js & ApexCharts:</strong> رسوم بيانية متقدمة</li>
                <li>🔍 <strong>بحث ذكي:</strong> نتائج فورية مع اقتراحات</li>
                <li>📱 <strong>تصميم متجاوب:</strong> يعمل على جميع الأجهزة</li>
                <li>⚡ <strong>أداء عالي:</strong> تحميل سريع وسلس</li>
                <li>🎨 <strong>تأثيرات بصرية:</strong> انيميشن وتحولات سلسة</li>
                <li>🔐 <strong>أمان متقدم:</strong> حماية شاملة للبيانات</li>
            </ul>
        </div>

        <div class="test-item info">
            <h2>📋 المميزات المتوفرة</h2>
            <h3>للعملاء:</h3>
            <ul>
                <li>تصفح قائمة الطعام مع الصور والأسعار</li>
                <li>إضافة الأطباق إلى السلة</li>
                <li>تعديل الكميات في السلة</li>
                <li>إتمام الطلب مع معلومات التوصيل</li>
                <li>حجز الطاولات</li>
                <li>تصفية الأطباق حسب الفئات</li>
            </ul>
            
            <h3>للإدارة:</h3>
            <ul>
                <li>لوحة تحكم مع الإحصائيات</li>
                <li>إدارة الطلبات وتتبع حالتها</li>
                <li>إدارة قائمة الطعام (إضافة/تعديل/حذف)</li>
                <li>إدارة الحجوزات</li>
                <li>إدارة العملاء</li>
                <li>التقارير والإحصائيات</li>
                <li>الإعدادات العامة</li>
            </ul>
        </div>

        <div class="test-item success">
            <h2>🛠️ التقنيات المستخدمة</h2>
            <ul>
                <li><strong>HTML5:</strong> هيكل الصفحات</li>
                <li><strong>CSS3:</strong> التصميم المتجاوب مع متغيرات CSS</li>
                <li><strong>JavaScript:</strong> الوظائف التفاعلية</li>
                <li><strong>LocalStorage:</strong> حفظ بيانات السلة</li>
                <li><strong>Font Awesome:</strong> الأيقونات</li>
                <li><strong>Google Fonts:</strong> خط Cairo العربي</li>
            </ul>
        </div>

        <div class="test-item info">
            <h2>📱 التوافق</h2>
            <p>النظام متوافق مع:</p>
            <ul>
                <li>جميع المتصفحات الحديثة</li>
                <li>أجهزة سطح المكتب</li>
                <li>الأجهزة اللوحية</li>
                <li>الهواتف الذكية</li>
            </ul>
        </div>

        <div class="test-item success">
            <h2>🚀 كيفية الاستخدام</h2>
            <ol>
                <li>افتح <code>index.html</code> لتصفح الموقع كعميل</li>
                <li>افتح <code>admin.html</code> للوصول إلى لوحة الإدارة</li>
                <li>جرب إضافة أطباق إلى السلة</li>
                <li>اختبر عملية الطلب</li>
                <li>جرب حجز طاولة</li>
                <li>استكشف لوحة الإدارة</li>
            </ol>
        </div>

        <div class="test-item info">
            <h2>📞 معلومات المطعم</h2>
            <p><strong>اسم المطعم:</strong> بازوكا</p>
            <p><strong>العنوان:</strong> شارع الملك فهد، الرياض، المملكة العربية السعودية</p>
            <p><strong>الهاتف:</strong> +966 11 123 4567</p>
            <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
            <p><strong>ساعات العمل:</strong> يومياً من 11:00 ص إلى 12:00 م</p>
        </div>

        <!-- Developer Information -->
        <div class="test-item success">
            <h2>👨‍💻 معلومات المطور</h2>
            <div style="background: linear-gradient(135deg, #ff6b35, #e55a2b); color: white; padding: 2rem; border-radius: 15px; text-align: center;">
                <div style="display: flex; align-items: center; justify-content: center; gap: 1rem; margin-bottom: 1rem;">
                    <i class="fas fa-code" style="font-size: 2rem; animation: pulse 2s infinite;"></i>
                    <div>
                        <h3 style="margin: 0; font-size: 1.5rem;">م. محمد الأشرافي</h3>
                        <p style="margin: 0; opacity: 0.9;">مطور ومصمم مواقع احترافي</p>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1.5rem;">
                    <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px;">
                        <i class="fas fa-phone" style="font-size: 1.2rem; margin-bottom: 0.5rem;"></i>
                        <p style="margin: 0; font-weight: 600;">رقم الهاتف</p>
                        <a href="tel:+966532969067" style="color: white; text-decoration: none; font-size: 1.1rem;">0532969067</a>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px;">
                        <i class="fas fa-laptop-code" style="font-size: 1.2rem; margin-bottom: 0.5rem;"></i>
                        <p style="margin: 0; font-weight: 600;">التخصص</p>
                        <p style="margin: 0;">تطوير أنظمة ويب متقدمة</p>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 10px;">
                        <i class="fas fa-tools" style="font-size: 1.2rem; margin-bottom: 0.5rem;"></i>
                        <p style="margin: 0; font-weight: 600;">التقنيات</p>
                        <p style="margin: 0; font-size: 0.9rem;">HTML5, CSS3, JavaScript, React, Vue.js</p>
                    </div>
                </div>

                <div style="margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid rgba(255,255,255,0.2);">
                    <p style="margin: 0; font-style: italic; opacity: 0.9;">
                        "تم تطوير هذا النظام بأعلى معايير الجودة والاحترافية"
                    </p>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" class="btn" style="font-size: 18px; padding: 15px 30px;">
                🍽️ ابدأ التجربة الآن
            </a>
        </div>
    </div>

    <script>
        // إضافة بعض التفاعل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🍽️ نظام مطعم بازوكا جاهز للاستخدام!');
            
            // إضافة تأثير عند النقر على الأزرار
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
        });
    </script>

    <style>
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
    </style>
</body>
</html>
